using Godot;
using System;

public partial class JamMakerMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Control _control;
	private Sprite2D _panel;
	private Button _closeButton;
	private Button _berryJamButton;
	private Button _strawberryJamButton;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;
	private Label _amountToProduce;
	private Sprite2D _itemFront;

	private JamMaker _jamMaker;
	private ResourceType _selectedResource = ResourceType.BerryJam;
	private int _currentAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_control = GetNode<Control>("Control");
		_panel = GetNode<Sprite2D>("Control/Panel");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_berryJamButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListBerryJam/Button");
		_strawberryJamButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListStrawberryJam/Button");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");

		_closeButton.Pressed += OnCloseButtonPressed;
		_berryJamButton.Pressed += OnBerryJamButtonPressed;
		_strawberryJamButton.Pressed += OnStrawberryJamButtonPressed;
		_buttonMinusOne.Pressed += OnMinusOnePressed;
		_buttonPlusOne.Pressed += OnPlusOnePressed;
		_buttonSetOne.Pressed += OnSetOnePressed;
		_buttonSet25Percent.Pressed += OnSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnSet50PercentPressed;
		_buttonSetMax.Pressed += OnSetMaxPressed;
		_buttonProduce.Pressed += OnProducePressed;

		_panel.Visible = false;

		UpdateSelectedResource();

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("JamMakerMenu", this);
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Escape") && _panel.Visible)
		{
			CloseMenu();
		}
	}

	public void SetJamMaker(JamMaker jamMaker)
	{
		_jamMaker = jamMaker;
	}

	public void OpenMenu(JamMaker jamMaker)
	{
		_jamMaker = jamMaker;
		_selectedResource = ResourceType.BerryJam;
		_currentAmount = 1;
		UpdateSelectedResource();
		UpdateAmountDisplay();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}
		else
		{
			_panel.Visible = true;
		}
	}

	public void CloseMenu()
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}
		else
		{
			_panel.Visible = false;
		}

		if (MenuManager.Instance != null)
		{
			MenuManager.Instance.CloseMenu("JamMakerMenu");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnBerryJamButtonPressed()
	{
		_selectedResource = ResourceType.BerryJam;
		UpdateSelectedResource();
	}

	private void OnStrawberryJamButtonPressed()
	{
		_selectedResource = ResourceType.StrawberryJam;
		UpdateSelectedResource();
	}

	private void OnMinusOnePressed()
	{
		_currentAmount = Math.Max(1, _currentAmount - 1);
		UpdateAmountDisplay();
	}

	private void OnPlusOnePressed()
	{
		_currentAmount++;
		UpdateAmountDisplay();
	}

	private void OnSetOnePressed()
	{
		_currentAmount = 1;
		UpdateAmountDisplay();
	}

	private void OnSet25PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 4);
		UpdateAmountDisplay();
	}

	private void OnSet50PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 2);
		UpdateAmountDisplay();
	}

	private void OnSetMaxPressed()
	{
		_currentAmount = GetMaxCraftableAmount();
		UpdateAmountDisplay();
	}

	private void OnProducePressed()
	{
		if (_jamMaker != null && _jamMaker.CanCraft(_selectedResource, _currentAmount))
		{
			_jamMaker.StartCrafting(_selectedResource, _currentAmount);
			CloseMenu();
		}
	}

	private void UpdateSelectedResource()
	{
		if (_itemFront != null)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
		}
	}

	private void UpdateAmountDisplay()
	{
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _currentAmount.ToString();
		}
	}

	private int GetMaxCraftableAmount()
	{
		if (_jamMaker == null) return 1;

		int maxAmount = 1;

		switch (_selectedResource)
		{
			case ResourceType.BerryJam:
				// Berry jam requires 4 berries
				int berryAvailable = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Berry);
				maxAmount = Math.Max(1, berryAvailable / 4);
				break;
			case ResourceType.StrawberryJam:
				// Strawberry jam requires 4 strawberries
				int strawberryAvailable = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Strawberry);
				maxAmount = Math.Max(1, strawberryAvailable / 4);
				break;
		}

		return maxAmount;
	}

	// IMenu interface implementation
	public void OpenMenu()
	{
		if (_jamMaker != null)
		{
			OpenMenu(_jamMaker);
		}
	}

	public bool IsMenuOpen()
	{
		return _panel != null && _panel.Visible;
	}
}
