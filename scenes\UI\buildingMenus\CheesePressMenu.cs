using Godot;
using System;

public partial class CheesePressMenu : CanvasLayer, IMenu
{
	private AnimationPlayer _animationPlayer;
	private Control _control;
	private Sprite2D _panel;
	private Button _closeButton;
	private Button _cheeseButton;
	private Button _buttonMinusOne;
	private Button _buttonPlusOne;
	private Button _buttonSetOne;
	private Button _buttonSet25Percent;
	private Button _buttonSet50Percent;
	private Button _buttonSetMax;
	private Button _buttonProduce;
	private Label _amountToProduce;
	private Sprite2D _itemFront;

	private CheesePress _cheesePress;
	private ResourceType _selectedResource = ResourceType.Cheese;
	private int _currentAmount = 1;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_control = GetNode<Control>("Control");
		_panel = GetNode<Sprite2D>("Control/Panel");
		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		_cheeseButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemListCheese/Button");
		_buttonMinusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonMinusOne");
		_buttonPlusOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonPlusOne");
		_buttonSetOne = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetOne");
		_buttonSet25Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet25Percent");
		_buttonSet50Percent = GetNode<Button>("Control/Panel/InfoBoard/ButtonSet50Percent");
		_buttonSetMax = GetNode<Button>("Control/Panel/InfoBoard/ButtonSetMax");
		_buttonProduce = GetNode<Button>("Control/Panel/InfoBoard/ButtonProduce");
		_amountToProduce = GetNode<Label>("Control/Panel/InfoBoard/AmountToProduce");
		_itemFront = GetNode<Sprite2D>("Control/Panel/InfoBoard/ItemFront");

		_closeButton.Pressed += OnCloseButtonPressed;
		_cheeseButton.Pressed += OnCheeseButtonPressed;
		_buttonMinusOne.Pressed += OnMinusOnePressed;
		_buttonPlusOne.Pressed += OnPlusOnePressed;
		_buttonSetOne.Pressed += OnSetOnePressed;
		_buttonSet25Percent.Pressed += OnSet25PercentPressed;
		_buttonSet50Percent.Pressed += OnSet50PercentPressed;
		_buttonSetMax.Pressed += OnSetMaxPressed;
		_buttonProduce.Pressed += OnProducePressed;

		_panel.Visible = false;

		UpdateSelectedResource();

		// Register with MenuManager
		MenuManager.Instance?.RegisterMenu("CheesePressMenu", this);
	}

	public override void _Input(InputEvent @event)
	{
		if (@event.IsActionPressed("Escape") && _panel.Visible)
		{
			CloseMenu();
		}
	}

	public void SetCheesePress(CheesePress cheesePress)
	{
		_cheesePress = cheesePress;
	}

	public void OpenMenu(CheesePress cheesePress)
	{
		_cheesePress = cheesePress;
		_selectedResource = ResourceType.Cheese;
		_currentAmount = 1;
		UpdateSelectedResource();
		UpdateAmountDisplay();

		if (_animationPlayer != null && _animationPlayer.HasAnimation("Open"))
		{
			_animationPlayer.Play("Open");
		}
		else
		{
			_panel.Visible = true;
		}
	}

	public void CloseMenu()
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation("Close"))
		{
			_animationPlayer.Play("Close");
		}
		else
		{
			_panel.Visible = false;
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnCheeseButtonPressed()
	{
		_selectedResource = ResourceType.Cheese;
		UpdateSelectedResource();
	}

	private void OnMinusOnePressed()
	{
		_currentAmount = Math.Max(1, _currentAmount - 1);
		UpdateAmountDisplay();
	}

	private void OnPlusOnePressed()
	{
		_currentAmount++;
		UpdateAmountDisplay();
	}

	private void OnSetOnePressed()
	{
		_currentAmount = 1;
		UpdateAmountDisplay();
	}

	private void OnSet25PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 4);
		UpdateAmountDisplay();
	}

	private void OnSet50PercentPressed()
	{
		int maxAmount = GetMaxCraftableAmount();
		_currentAmount = Math.Max(1, maxAmount / 2);
		UpdateAmountDisplay();
	}

	private void OnSetMaxPressed()
	{
		_currentAmount = GetMaxCraftableAmount();
		UpdateAmountDisplay();
	}

	private void OnProducePressed()
	{
		if (_cheesePress != null && _cheesePress.CanCraft(_selectedResource, _currentAmount))
		{
			_cheesePress.StartCrafting(_selectedResource, _currentAmount);
			CloseMenu();
		}
	}

	private void UpdateSelectedResource()
	{
		if (_itemFront != null)
		{
			var texture = TextureManager.Instance?.GetResourceTexture(_selectedResource);
			_itemFront.Texture = texture;
		}
	}

	private void UpdateAmountDisplay()
	{
		if (_amountToProduce != null)
		{
			_amountToProduce.Text = _currentAmount.ToString();
		}
	}

	private int GetMaxCraftableAmount()
	{
		if (_cheesePress == null) return 1;

		// For cheese: requires 5 milk per cheese
		int milkAvailable = GameSaveData.Instance.PlayerResources.GetResourceQuantity(ResourceType.Milk);
		return Math.Max(1, milkAvailable / 5);
	}

	// IMenu interface implementation
	public void OpenMenu()
	{
		if (_cheesePress != null)
		{
			OpenMenu(_cheesePress);
		}
	}

	public bool IsMenuOpen()
	{
		return _panel != null && _panel.Visible;
	}
}
